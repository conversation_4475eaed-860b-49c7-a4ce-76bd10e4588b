"use client";

import { ArrowRight, Search, Tag, TrendingUp, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import type { PostMeta } from "@/lib/blog";
import { formatDate } from "@/lib/date";

interface BlogListClientProps {
  posts: PostMeta[];
}

const BlogListClient = ({ posts }: BlogListClientProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredPosts = posts.filter((post) => {
    const term = searchTerm.toLowerCase();
    return post.title.toLowerCase().includes(term) || post.excerpt.toLowerCase().includes(term);
  });

  const popularTags = Array.from(new Set(posts.flatMap((p) => p.tags)).values()).slice(0, 5);

  return (
    <div className="grid lg:grid-cols-4 gap-8">
      {/* Main Content */}
      <div className="lg:col-span-3">
        {/* Blog Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {filteredPosts.map((post, index) => (
            <Link
              key={post.slug}
              href={`/blog/${post.slug}`}
              className="group block bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl hover:bg-white/10 transition-all duration-300 overflow-hidden animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={post.thumbnail}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-3 line-clamp-2 font-space-grotesk">
                  {post.title}
                </h3>
                <p className="text-gray-400 mb-4 line-clamp-3 leading-relaxed">{post.excerpt}</p>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{post.author}</p>
                      <p className="text-xs text-gray-400">{formatDate(post.date)}</p>
                    </div>
                  </div>
                  <span className="text-sm text-gray-400">{post.readTime}</span>
                </div>
                <div className="group/btn inline-flex items-center gap-2 text-electric-blue font-semibold">
                  Read Article
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="sticky top-24 space-y-8">
          {/* Search */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2 font-space-grotesk">
              <Search className="w-5 h-5 text-electric-blue" />
              Search Articles
            </h3>
            <div className="relative">
              <input
                type="text"
                placeholder="Search blog posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:ring-2 focus:ring-electric-blue focus:border-transparent transition-all"
              />
              <Search className="absolute right-3 top-3.5 w-4 h-4 text-gray-400" />
            </div>
          </div>

          {/* Popular Tags */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2 font-space-grotesk">
              <Tag className="w-5 h-5 text-electric-blue" />
              Popular Tags
            </h3>
            <div className="flex flex-wrap gap-2">
              {popularTags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-white/10 text-gray-300 rounded-full text-sm hover:bg-electric-blue/20 hover:text-electric-blue transition-colors cursor-pointer border border-white/20"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>

          {/* Recent Posts */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h3 className="font-bold text-white mb-4 flex items-center gap-2 font-space-grotesk">
              <TrendingUp className="w-5 h-5 text-electric-blue" />
              Recent Posts
            </h3>
            <div className="space-y-4">
              {posts.slice(0, 3).map((post) => (
                <Link key={post.slug} href={`/blog/${post.slug}`} className="block group">
                  <h4 className="font-medium text-white group-hover:text-electric-blue transition-colors line-clamp-2 mb-1">
                    {post.title}
                  </h4>
                  <p className="text-sm text-gray-400">{formatDate(post.date)}</p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogListClient;
