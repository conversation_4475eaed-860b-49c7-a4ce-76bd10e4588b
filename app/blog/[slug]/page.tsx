import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Calendar, Clock, Share2, Tag, User } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { getAllPosts, getPostBySlug, type Post } from "@/lib/blog";
import { formatDate } from "@/lib/date";
import { Button } from "../../components/ui/button";

const BlogDetailPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  let post: Post;
  try {
    post = await getPostBySlug(slug);
  } catch {
    notFound();
  }

  const allPosts = await getAllPosts();
  const relatedPosts = allPosts.filter((p) => p.slug !== slug).slice(0, 3);

  return (
    <article className="pt-20 bg-black min-h-screen">
      {/* Hero Section */}
      <div className="relative py-20 bg-black overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-electric-blue/10 to-transparent rounded-full blur-3xl animate-float" />
        <div className="relative z-10 max-w-4xl mx-auto px-6">
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-electric-blue hover:text-white transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight font-space-grotesk">
            {post.title}
          </h1>
          <div className="flex flex-wrap items-center gap-6 text-gray-400 mb-8">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <span className="text-white font-medium">{post.author}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(post.date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>{post.readTime}</span>
            </div>
          </div>
          <p className="text-xl text-gray-400 leading-relaxed">{post.excerpt}</p>
        </div>
      </div>

      {/* Content */}
      <div className="bg-black">
        <div className="max-w-4xl mx-auto px-6 py-16">
          {/* Share Buttons */}
          <div className="flex items-center justify-between mb-12 pb-6 border-b border-white/10">
            <div className="flex items-center gap-4">
              <span className="text-gray-400 font-medium">Share this article:</span>
              <div className="flex gap-2">
                <button
                  className="p-2 bg-white/10 text-electric-blue rounded-lg hover:bg-electric-blue/20 transition-colors"
                  type="button"
                >
                  <Share2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            <div className="flex items-center gap-2 text-gray-400">
              <BookOpen className="w-4 h-4" />
              <span className="text-sm">{post.readTime}</span>
            </div>
          </div>
          <div
            className="prose prose-lg max-w-none prose-headings:text-white prose-headings:font-bold prose-headings:font-space-grotesk prose-p:text-gray-300 prose-p:leading-relaxed prose-a:text-electric-blue prose-a:no-underline hover:prose-a:underline prose-strong:text-white prose-ul:text-gray-300 prose-blockquote:border-electric-blue prose-blockquote:bg-white/5 prose-blockquote:p-6 prose-blockquote:rounded-xl"
            // biome-ignore lint/security/noDangerouslySetInnerHtml: content is sanitized beforehand
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
          {/* Tags */}
          <div className="mt-12 pt-8 border-t border-white/10">
            <div className="flex items-center gap-2 mb-4">
              <Tag className="w-5 h-5 text-electric-blue" />
              <span className="font-medium text-white font-space-grotesk">Tags:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-white/10 text-gray-300 rounded-full text-sm hover:bg-electric-blue/20 hover:text-electric-blue transition-colors cursor-pointer border border-white/20"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <div className="bg-black py-16 border-t border-white/10">
          <div className="max-w-7xl mx-auto px-6">
            <h2 className="text-3xl font-bold text-white mb-12 text-center font-space-grotesk">
              Related Articles
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {relatedPosts.map((p, index) => (
                <Link
                  key={p.slug}
                  href={`/blog/${p.slug}`}
                  className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl hover:bg-white/10 transition-all duration-300 overflow-hidden animate-fade-in-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="aspect-video bg-white/10 flex items-center justify-center">
                    <span className="text-gray-400">Image</span>
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-3 line-clamp-2 font-space-grotesk">
                      {p.title}
                    </h3>
                    <p className="text-sm text-gray-400">{formatDate(p.date)}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* CTA Section */}
      <div className="bg-black py-16 border-t border-white/10">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4 font-space-grotesk">
            Ready to Start Trading?
          </h2>
          <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
            Apply the strategies you've learned with our professional AI trading platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group">
              Start Free Trial
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="secondary" size="lg">
              View Trading Bots
            </Button>
          </div>
        </div>
      </div>
    </article>
  );
};

export default BlogDetailPage;
