import { ArrowR<PERSON> } from "lucide-react";
import { getAllPosts } from "@/lib/blog";
import { But<PERSON> } from "../components/ui/button";
import BlogListClient from "./BlogListClient";

const BlogPage = async () => {
  const posts = await getAllPosts();
  if (posts.length === 0) {
    return <p className="text-white">No posts found.</p>;
  }
  return (
    <>
      {/* Hero Section */}
      <section className="pt-32 pb-20 bg-black relative overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-electric-blue/10 to-transparent rounded-full blur-3xl animate-float" />
        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Trading Insights & AI Strategies
          </h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Expert insights on automated trading, AI strategies, market analysis, and platform
            updates to help you stay ahead.
          </p>
        </div>
      </section>

      <div className="bg-black min-h-screen">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <BlogListClient posts={posts} />

          {/* Bottom CTA */}
          <div className="mt-20 pt-16 border-t border-white/10 text-center">
            <h2 className="text-3xl font-bold text-white mb-4 font-space-grotesk">
              Ready to Apply These Strategies?
            </h2>
            <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
              Start your free trial and let our AI bots implement professional trading strategies
              for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="group">
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="secondary" size="lg">
                Browse Trading Bots
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogPage;
