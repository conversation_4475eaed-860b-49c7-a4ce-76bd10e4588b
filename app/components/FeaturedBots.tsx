import { Activity, ArrowRight, Shield, TrendingUp } from "lucide-react";
import { But<PERSON> } from "./ui/button";

const FeaturedBots = () => {
  const bots = [
    {
      name: "AlphaX",
      roi: "+27%",
      period: "30 days",
      strategy: "Momentum + AI signals",
      icon: TrendingUp,
      status: "Active",
    },
    {
      name: "BetaShield",
      roi: "+15%",
      period: "low drawdown",
      strategy: "Futures, Risk-Controlled",
      icon: Shield,
      status: "Active",
    },
    {
      name: "GammaQuant",
      roi: "+38%",
      period: "sideways market",
      strategy: "Mean-reversion + RL",
      icon: Activity,
      status: "Active",
    },
  ];

  return (
    <section className="py-32 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Top Performing Bots
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Professional AI trading strategies with proven track records and transparent performance
            metrics.
          </p>
        </div>

        {/* Clean Bot List Layout */}
        <div className="space-y-8">
          {bots.map((bot, index) => {
            const Icon = bot.icon;
            return (
              <div
                key={index}
                className="group flex flex-col md:flex-row items-start md:items-center justify-between p-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl hover:bg-white/10 transition-all duration-300 animate-fade-in-up"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* Bot Info */}
                <div className="flex items-center gap-6 mb-6 md:mb-0">
                  <div className="w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-white" />
                  </div>

                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-2xl font-bold text-white font-space-grotesk">
                        {bot.name}
                      </h3>
                      <div className="px-2 py-1 bg-green-500/20 text-green-400 text-xs font-medium rounded-full">
                        {bot.status}
                      </div>
                    </div>
                    <p className="text-gray-400 mb-1">
                      <span className="font-medium">Strategy:</span> {bot.strategy}
                    </p>
                    <p className="text-sm text-gray-500">Performance period: {bot.period}</p>
                  </div>
                </div>

                {/* Performance & Action */}
                <div className="flex items-center gap-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-1">{bot.roi}</div>
                    <div className="text-sm text-gray-400">ROI</div>
                  </div>

                  <Button variant="primary" className="group">
                    Follow Bot
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Button variant="secondary" size="lg">
            View All Bots
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedBots;
