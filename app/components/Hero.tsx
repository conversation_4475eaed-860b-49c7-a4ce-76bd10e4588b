import { ArrowR<PERSON>, Play } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0">
        {/* Minimal Grid Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px)
              `,
              backgroundSize: "60px 60px",
            }}
          />
        </div>

        {/* Subtle Gradient Orbs */}
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-electric-blue/10 to-transparent rounded-full blur-3xl animate-float" />
        <div
          className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-white/5 to-transparent rounded-full blur-3xl animate-float"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-32 pb-20">
        <div className="text-center max-w-4xl mx-auto">
          {/* Status Badge */}
          <div className="mb-8 animate-fade-in-up">
            <div className="inline-flex items-center gap-2 bg-white/5 backdrop-blur-sm border border-white/10 rounded-full px-4 py-2 text-sm text-gray-300">
              <div className="w-2 h-2 bg-electric-blue rounded-full animate-pulse" />
              Professional AI Trading Platform
            </div>
          </div>

          {/* Main Headline */}
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-6 leading-none animate-fade-in-up delay-200 tracking-tight font-space-grotesk">
              AI-Powered
              <br />
              <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent animate-gradient bg-300">
                Crypto Trading
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-400 mb-12 animate-fade-in-up delay-300 leading-relaxed max-w-3xl mx-auto">
              Eliminate emotional bias with institutional-grade quantitative strategies.
              Professional AI trading platform trusted by thousands of traders worldwide.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 animate-fade-in-up delay-400">
            <Button size="lg" className="group">
              Get Started Free
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="secondary" size="lg" className="group">
              <Play className="w-5 h-5 mr-2" />
              Watch Demo
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="animate-fade-in-up delay-500">
            <p className="text-sm text-gray-500 mb-6">Trusted by 10,000+ traders worldwide</p>
            <div className="flex justify-center items-center gap-8 opacity-60">
              <div className="text-2xl font-bold text-white">$50M+</div>
              <div className="w-px h-8 bg-gray-700" />
              <div className="text-2xl font-bold text-white">99.9%</div>
              <div className="w-px h-8 bg-gray-700" />
              <div className="text-2xl font-bold text-white">24/7</div>
            </div>
            <div className="flex justify-center items-center gap-8 text-xs text-gray-500 mt-2">
              <div>Trading Volume</div>
              <div>Uptime</div>
              <div>Support</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
