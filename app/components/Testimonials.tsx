import { Quote, <PERSON> } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      quote:
        "LightQuant's AI bots generated 23% returns in my first quarter. The transparency and real-time analytics give me complete confidence in every trade.",
      author: "<PERSON>",
      role: "Portfolio Manager",
      location: "Singapore",
      avatar:
        "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "Finally, a platform that combines institutional-grade AI with retail accessibility. The risk management features are exceptional.",
      author: "<PERSON>",
      role: "Quantitative Trader",
      location: "London",
      avatar:
        "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "The automated strategies have consistently outperformed my manual trading. LightQuant has revolutionized my approach to crypto markets.",
      author: "<PERSON>",
      role: "Investment Advisor",
      location: "New York",
      avatar:
        "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
  ];

  return (
    <section className="py-32 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Trusted by Professionals
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Join thousands of traders who have transformed their trading with our AI-powered
            platform.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: testimonials array order is constant
              key={index}
              className="group bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300 animate-fade-in-up"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              {/* Quote Icon */}
              <Quote className="w-8 h-8 text-white/40 mb-6" />

              {/* Testimonial Text */}
              <p className="text-lg text-gray-300 leading-relaxed mb-8 italic">
                "{testimonial.quote}"
              </p>

              {/* Author Info */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {testimonial.author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </span>
                </div>
                <div>
                  <p className="font-semibold text-white">{testimonial.author}</p>
                  <p className="text-sm text-gray-400">{testimonial.role}</p>
                  <p className="text-xs text-gray-500">{testimonial.location}</p>
                </div>
              </div>

              {/* Star Rating */}
              <div className="flex gap-1 mt-6">
                {[...Array(5)].map((_, starIndex) => (
                  <Star key={starIndex} className="w-4 h-4 text-electric-blue fill-current" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
