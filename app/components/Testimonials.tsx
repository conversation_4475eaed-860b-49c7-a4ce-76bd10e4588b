import { Quo<PERSON>, <PERSON> } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      quote:
        "LightQuant's AI bots generated 23% returns in my first quarter. The transparency and real-time analytics give me complete confidence in every trade.",
      author: "<PERSON>",
      role: "Portfolio Manager",
      location: "Singapore",
      avatar:
        "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "Finally, a platform that combines institutional-grade AI with retail accessibility. The risk management features are exceptional.",
      author: "<PERSON>",
      role: "Quantitative Trader",
      location: "London",
      avatar:
        "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "The automated strategies have consistently outperformed my manual trading. LightQuant has revolutionized my approach to crypto markets.",
      author: "<PERSON>",
      role: "Investment Advisor",
      location: "New York",
      avatar:
        "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "The platform's risk management tools have saved me from significant losses during volatile periods. Absolutely game-changing technology.",
      author: "Emma Thompson",
      role: "Crypto Fund Manager",
      location: "Toronto",
      avatar:
        "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "LightQuant's backtesting capabilities allowed me to validate strategies before going live. The results speak for themselves.",
      author: "James Park",
      role: "Algorithmic Trader",
      location: "Seoul",
      avatar:
        "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
    {
      quote:
        "The AI continuously adapts to market conditions. I've seen consistent performance even during the most challenging market cycles.",
      author: "Lisa Anderson",
      role: "Investment Strategist",
      location: "Sydney",
      avatar:
        "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face",
    },
  ];

  // Duplicate testimonials for seamless loop
  const duplicatedTestimonials = [...testimonials, ...testimonials];

  return (
    <section className="py-32 bg-black overflow-hidden">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Trusted by Professionals
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Join thousands of traders who have transformed their trading with our AI-powered
            platform.
          </p>
        </div>

        {/* Marquee Container */}
        <div className="relative">
          {/* Gradient Overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-black to-transparent z-10 pointer-events-none" />
          <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-black to-transparent z-10 pointer-events-none" />

          {/* Marquee Track */}
          <div className="flex animate-marquee hover:pause-marquee">
            {duplicatedTestimonials.map((testimonial, index) => (
              <div
                key={`${testimonial.author}-${index}`}
                className="flex-shrink-0 w-96 mx-4 group bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300"
              >
                {/* Quote Icon */}
                <Quote className="w-8 h-8 text-white/40 mb-6" />

                {/* Testimonial Text */}
                <p className="text-lg text-gray-300 leading-relaxed mb-8 italic line-clamp-4">
                  "{testimonial.quote}"
                </p>

                {/* Author Info */}
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-semibold">
                      {testimonial.author
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div className="min-w-0">
                    <p className="font-semibold text-white truncate">{testimonial.author}</p>
                    <p className="text-sm text-gray-400 truncate">{testimonial.role}</p>
                    <p className="text-xs text-gray-500 truncate">{testimonial.location}</p>
                  </div>
                </div>

                {/* Star Rating */}
                <div className="flex gap-1 mt-6">
                  {[...Array(5)].map((_, starIndex) => (
                    <Star
                      key={`star-${testimonial.author}-${starIndex}`}
                      className="w-4 h-4 text-electric-blue fill-current"
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
