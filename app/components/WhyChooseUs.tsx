import { Bar<PERSON>hart3, Brain, Network, Shield, Zap } from "lucide-react";
import { But<PERSON> } from "./ui/button";

const WhyChooseUs = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Intelligence",
      description:
        "Advanced machine learning algorithms continuously optimize trading strategies based on real-time market data and historical performance patterns.",
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description:
        "Bank-grade encryption and API-only integration ensure your funds remain secure while our bots execute trades on your behalf.",
    },
    {
      icon: BarChart3,
      title: "Complete Transparency",
      description:
        "Full access to historical performance data, real-time metrics, and detailed analytics for every trading strategy and bot.",
    },
    {
      icon: Zap,
      title: "Instant Deployment",
      description:
        "Deploy professional trading strategies with a single click. No coding knowledge required, no complex setup processes.",
    },
    {
      icon: Network,
      title: "Multi-Exchange Support",
      description:
        "Seamlessly trade across major exchanges including Binance, Bybit, and OKX with unified portfolio management.",
    },
  ];

  return (
    <section className="py-32 bg-gray-950">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Why Choose LightQuant
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Professional-grade trading infrastructure designed for serious traders who demand
            results, transparency, and reliability.
          </p>
        </div>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {features.map((feature, index) => {
            const Icon = feature.icon;

            return (
              <div
                key={feature.title}
                className="group text-center animate-fade-in-up hover:scale-105 transition-all duration-300"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {/* Icon */}
                <div className="w-20 h-20 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-electric-blue/20 group-hover:scale-110 transition-all duration-300 border border-white/10 group-hover:border-electric-blue/30">
                  <Icon className="w-10 h-10 text-white group-hover:text-electric-blue transition-colors duration-300" />
                </div>

                {/* Title */}
                <h3 className="text-xl md:text-2xl font-bold text-white mb-4 font-space-grotesk group-hover:text-electric-blue transition-colors duration-300">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 animate-fade-in-up delay-1000">
          <p className="text-lg text-gray-400 mb-8 max-w-2xl mx-auto">
            Join thousands of traders who trust LightQuant for their automated trading needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="primary" size="lg">
              Start Trading Now
            </Button>
            <Button variant="secondary" size="lg">
              Learn More
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
