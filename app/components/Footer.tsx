import { Mail, MessageCircle, Send, X } from "lucide-react";
import Link from "next/link";

const Footer = () => {
  return (
    <footer className="bg-black text-white pt-20 border-t border-white/10">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-12 mb-16">
          {/* Brand */}
          <div className="md:col-span-1">
            <h3 className="text-2xl font-bold mb-6 font-space-grotesk">LightQuant</h3>
            <p className="text-gray-400 leading-relaxed mb-6">
              Professional AI-powered crypto trading platform. Eliminate emotional bias with
              institutional-grade quantitative strategies.
            </p>
            <div className="text-sm text-gray-500">© 2025 LightQuant. All rights reserved.</div>
          </div>

          {/* Platform Links */}
          <div>
            <h4 className="font-semibold mb-6 text-white font-space-grotesk">Platform</h4>
            <ul className="space-y-3 text-gray-400">
              <li>
                <Link href="/features" className="hover:text-white transition-colors duration-300">
                  Features
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="hover:text-white transition-colors duration-300">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/dashboard" className="hover:text-white transition-colors duration-300">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/blog" className="hover:text-white transition-colors duration-300">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="font-semibold mb-6 text-white font-space-grotesk">Company</h4>
            <ul className="space-y-3 text-gray-400">
              <li>
                <Link href="/about" className="hover:text-white transition-colors duration-300">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-white transition-colors duration-300">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-white transition-colors duration-300">
                  FAQ
                </Link>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors duration-300">
                  API Documentation
                </a>
              </li>
            </ul>
          </div>

          {/* Legal & Contact */}
          <div>
            <h4 className="font-semibold mb-6 text-white font-space-grotesk">Legal & Support</h4>
            <ul className="space-y-3 text-gray-400 mb-8">
              <li>
                <a href="/terms" className="hover:text-white transition-colors duration-300">
                  Terms of Service
                </a>
              </li>
              <li>
                <a href="/privacy" className="hover:text-white transition-colors duration-300">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="/risk" className="hover:text-white transition-colors duration-300">
                  Risk Disclosure
                </a>
              </li>
            </ul>

            <div className="space-y-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300"
              >
                <Mail className="w-4 h-4" />
                <EMAIL>
              </a>

              <div className="flex gap-3">
                <a
                  href="https://twitter.com/lightquant"
                  className="p-2 bg-white/5 rounded-lg hover:bg-white/10 transition-colors duration-300"
                  aria-label="Twitter"
                >
                  <X className="w-5 h-5" />
                </a>
                <a
                  href="https://discord.gg/lightquant"
                  className="p-2 bg-white/5 rounded-lg hover:bg-white/10 transition-colors duration-300"
                  aria-label="Discord"
                >
                  <MessageCircle className="w-5 h-5" />
                </a>
                <a
                  href="https://t.me/lightquant"
                  className="p-2 bg-white/5 rounded-lg hover:bg-white/10 transition-colors duration-300"
                  aria-label="Telegram"
                >
                  <Send className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
