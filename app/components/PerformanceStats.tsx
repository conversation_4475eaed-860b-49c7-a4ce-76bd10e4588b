"use client";

import { Link, TrendingDown, TrendingUp, Users } from "lucide-react";
import { useEffect, useRef, useState } from "react";

// Custom hook for counter animation
const useCountUp = (end: number, duration = 2000, shouldStart = false) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if (!shouldStart || hasStarted) return;

    setHasStarted(true);
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      // Easing function for smooth animation
      const easeOutQuart = 1 - (1 - progress) ** 4;
      setCount(Math.floor(end * easeOutQuart));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, shouldStart, hasStarted]);

  return count;
};

const PerformanceStats = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Animated counters
  const roiCount = useCountUp(21.5, 2000, isVisible);
  const tradersCount = useCountUp(10000, 2500, isVisible);
  const uptimeCount = useCountUp(99.9, 2000, isVisible);

  const stats = [
    {
      icon: TrendingUp,
      value: `+${roiCount.toFixed(1)}%`,
      label: "Average monthly ROI",
      description: "Across all active bots",
    },
    {
      icon: Users,
      value: `${tradersCount.toLocaleString()}+`,
      label: "Active traders",
      description: "Worldwide community",
    },
    {
      icon: Link,
      value: `${uptimeCount.toFixed(1)}%`,
      label: "Platform uptime",
      description: "Enterprise reliability",
    },
    {
      icon: TrendingDown,
      value: "24/7",
      label: "Market monitoring",
      description: "Continuous operation",
    },
  ];

  return (
    <section ref={sectionRef} className="py-32 bg-black">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Trusted by Thousands
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Our platform delivers consistent results with institutional-grade infrastructure and
            proven performance metrics.
          </p>
        </div>

        {/* Clean Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={stat.label}
                className="text-center group animate-fade-in-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                {/* Icon */}
                <div className="w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon className="w-8 h-8 text-white" />
                </div>

                {/* Value */}
                <div className="text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-105 transition-transform duration-300 font-space-grotesk">
                  {stat.value}
                </div>

                {/* Label */}
                <h3 className="text-lg font-semibold text-white mb-1">{stat.label}</h3>

                {/* Description */}
                <p className="text-sm text-gray-400">{stat.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default PerformanceStats;
