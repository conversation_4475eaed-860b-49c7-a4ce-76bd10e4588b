import { <PERSON><PERSON><PERSON>, Zap } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";

const FinalCTA = () => {
  return (
    <section className="py-32 bg-black relative overflow-hidden">
      {/* Subtle Background Effects */}
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-electric-blue/10 to-transparent rounded-full blur-3xl animate-float" />
      <div
        className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-white/5 to-transparent rounded-full blur-3xl animate-float"
        style={{ animationDelay: "2s" }}
      />

      <div className="relative z-10 max-w-4xl mx-auto px-6 text-center">
        <div className="mb-12">
          {/* Icon */}
          <div className="w-20 h-20 bg-electric-blue/20 rounded-xl flex items-center justify-center mx-auto mb-8">
            <Zap className="w-10 h-10 text-electric-blue" />
          </div>

          {/* Headline */}
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up font-space-grotesk">
            Start Trading Like a Pro
          </h2>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-gray-400 mb-8 leading-relaxed animate-fade-in-up delay-200 max-w-3xl mx-auto">
            Join thousands of traders who have automated their success with LightQuant's AI-powered
            platform.
          </p>

          {/* Benefits */}
          <div className="flex flex-wrap justify-center gap-6 mb-12 text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-electric-blue rounded-full" />
              <span>7-day free trial</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-electric-blue rounded-full" />
              <span>No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-electric-blue rounded-full" />
              <span>Cancel anytime</span>
            </div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up delay-400">
          <Button size="xl" className="group">
            Get Started Free
            <ArrowRight className="w-6 h-6 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>

          <Button variant="secondary" size="xl">
            View Live Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;
